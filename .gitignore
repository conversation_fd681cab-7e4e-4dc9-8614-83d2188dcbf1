# =============================
# 通用操作系统文件
# =============================
# macOS
.DS_Store
.AppleDouble
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
.directory
.Trash-*

# =============================
# 开发环境/编辑器
# =============================
# IDE
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 文本编辑器
*~
*.bak
*.swp
*.swo

# =============================
# 语言/框架相关
# =============================
# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Python
__pycache__/
*.py[cod]
*.pyo
*.so
.Python
venv/
env/
.venv/
*.egg-info/
*.egg
dist/
build/

# Java
*.class
*.jar
*.war
*.ear
*.iml
.gradle/
build/
out/

# Ruby
*.gem
.bundle/
.ruby-version

# Go
bin/
pkg/
*.exe
*.test

# Rust
/target/
**/*.rs.bk

# =============================
# 项目构建/依赖
# =============================
# 日志文件
*.log
logs/

# 依赖目录
jspm_packages/
bower_components/

# 调试文件
*.pdb
*.idb
*.ilk

# 测试文件
coverage/
*.lcov
.nyc_output/

# =============================
# 配置文件/敏感数据
# =============================
# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production

# 证书/密钥
*.key
*.pem
*.crt
*.pub

# =============================
# 其他
# =============================
# 压缩文件
*.zip
*.tar.gz
*.tgz

# 系统文件
.DS_Store
Thumbs.db

downloaded.db